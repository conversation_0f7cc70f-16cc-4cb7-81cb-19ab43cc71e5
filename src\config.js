import steps from '@src/steps'

export default {
  apiUrl: {
    'localhost:8000': 'http://127.0.0.1',
    'host.homeworksenergy.com': 'https://hostapi.homeworksenergy.com',
    'hosttest.homeworksenergy.com': 'https://hosttestapi.homeworksenergy.com',
  },
  devPort: {
    heaSlots: '3001',
    heaSubmit: '3002',
    zipCodeValid: '3003',
    hvacOpenings: '3004',
    hvacServiceSubmit: '3005',
    empRefCode: '3006',
    hvacSalesSubmit: '3007',
  },
  // Steps in order
  steps: [
    steps.ChoosePath,
    steps.Eligibility,
    steps.ContactInfo,
    steps.Schedule,
    steps.Success,
  ],

  tetraUrl:
    'https://www.tetra.com/services/home-page-signup-form?utm_source=HWE&utm_medium=HWE',

  // When a user changes a value, all fields that follow except for those with the following names will be reset
  clearFollowingValuesExempt: ['contactInfo'],

  // User is ineligible when one of the following matches
  ineligibleValues: [
    {
      when: 'canService',
      is: 'no',
      message:
        'Unfortunately we do not service your area for heating and cooling sales visits at this time.',
    },
    {
      when: 'rentOrOwn',
      is: 'rent',
      message:
        'HomeWorks Energy would love to help you with your needs. Our online scheduler only supports homeowners at this time. \n\n Please call (************* ext. 8 to schedule an appointment.',
    },
    {
      when: 'twoYears',
      is: 'yes',
      message:
        'Sorry, your home only qualifies for a Mass Save Energy Assessment once every two years.\n\nFor more information, please call a HomeWorks Energy Specialist at (*************.',
    },
    {
      when: ['singleOrMulti', 'path'],
      is: (singleOrMulti, path) =>
        singleOrMulti === '5+ Multi-Family' && path === 'hea',
      message:
        'HomeWorks Energy currently only serves single family and multi-family homes of 2-4 units. To access Mass Save program benefits for larger multi-family buildings please see the information on the Mass Save website below: https://www.masssave.com/en/saving/energy-assessments/multi-family-facilities-5-units-plus',
    },
    {
      when: ['familySize', 'path'],
      is: (familySize, path) => ['5+'].includes(familySize) && path === 'hvac',
      message:
        'HomeWorks Energy would love the opportunity to serve your multi-family home for a sales appointment. Our online scheduler currently supports only Single-Family and multi-family homes of 1-4 units.\n\nPlease call (************* ext. 2 to speak with a representative.',
    },
    {
      when: ['howHeated', 'electricProvider'],
      is: (howHeated, electricProvider) =>
        howHeated !== 'Gas' && electricProvider === 'Municipal',
      message:
        'Sorry, Mass Save does not provide benefits to customers whose primary utility is not a program sponsor.\n\nFor more information, please call a HomeWorks Energy Specialist at (*************.',
    },
    {
      when: ['homeAge'],
      is: (homeAge) => homeAge === 'new',
      message: 'Sorry, we do not service new constructions',
    },
    {
      when: ['paymentAssistance', 'gasProvider', 'electricProvider'],
      is: (paymentAssistance, gasProvider, electricProvider) =>
        paymentAssistance === 'yes' &&
        (gasProvider === 'Berkshire Gas' ||
          electricProvider === 'Berkshire Gas'),
      message:
        'Berkshire Gas customers, please call us at 413-308-6690 to arrange your home energy assessment',
    },
    {
      when: ['fuelAssistance', 'salesOrService', 'path'],
      is: (fuelAssistance, salesOrService, path) =>
        fuelAssistance === 'yes' &&
        salesOrService === 'sales' &&
        path === 'hvac',
      message:
        'Based on your utility discount rate, we recommend starting with a Mass Save Home Energy Assessment to help you qualify for additional savings and rebates.\n\nPlease go back and select "Mass Save HEA" to schedule your Energy Assessment, or call (************* for assistance.',
    },
  ],

  // Zip Code check for Columbia gas, after Eversource aquire Columbia Gas, users are now entering Gas source as Eversource but HWE still considers them under Columbia Gas and the online scheduler should get slots for Columbia Gas comparing the zip code
  columbiaGasZipCodes: {
    otherRegions: [
      '01001',
      '01013',
      '01014',
      '01020',
      '01021',
      '01022',
      '01027',
      '01028',
      '01033',
      '01057',
      '01060',
      '01061',
      '01063',
      '01069',
      '01075',
      '01077',
      '01089',
      '01090',
      '01101',
      '01102',
      '01103',
      '01104',
      '01105',
      '01106',
      '01107',
      '01108',
      '01109',
      '01111',
      '01115',
      '01116',
      '01118',
      '01119',
      '01128',
      '01129',
      '01138',
      '01139',
      '01144',
      '01152',
      '01199',
      '01756',
      '01810',
      '01840',
      '01841',
      '01842',
      '01843',
      '01844',
      '01845',
      '02019',
      '02021',
      '02032',
      '02035',
      '02038',
      '02048',
      '02050',
      '02051',
      '02052',
      '02053',
      '02056',
      '02059',
      '02060',
      '02061',
      '02066',
      '02067',
      '02071',
      '02072',
      '02081',
      '02093',
      '02301',
      '02302',
      '02303',
      '02304',
      '02305',
      '02322',
      '02324',
      '02325',
      '02331',
      '02332',
      '02333',
      '02334',
      '02338',
      '02339',
      '02340',
      '02341',
      '02343',
      '02347',
      '02348',
      '02356',
      '02357',
      '02358',
      '02359',
      '02368',
      '02375',
      '02379',
      '02703',
      '02715',
      '02760',
      '02761',
      '02763',
      '02764',
      '02766',
      '02767',
      '02768',
      '02769',
      '02771',
      '02779',
      '02780',
      '02783',
      '05501',
      '05544',
    ],
    capeCod: [
      '02537',
      '02542',
      '02561',
      '02562',
      '02563',
      '02536',
      '02540',
      '02541',
      '02543',
      '02556',
      '02565',
      '02574',
      '02534',
      '02553',
      '02559',
      '02532',
      '02630',
      '02664',
      '02631',
      '02632',
      '02633',
      '02635',
      '02635',
      '02636',
      '02637',
      '02638',
      '02639',
      '02641',
      '02643',
      '02642',
      '02644',
      '02645',
      '02646',
      '02601',
      '02647',
      '02649',
      '02649',
      '02650',
      '02651',
      '02652',
      '02653',
      '02657',
      '02659',
      '02660',
      '02661',
      '02662',
      '02663',
      '02664',
      '02666',
      '02667',
      '02668',
      '02669',
      '02670',
      '02671',
      '02672',
      '02673',
      '02675',
    ],
  },
}
