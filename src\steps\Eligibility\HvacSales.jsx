import React, { useEffect } from 'react'
import PropTypes from 'prop-types'
import * as yup from 'yup'
import { Field } from 'formik'

import ConditionalBlock from '@components/ui/ConditionalBlock'
import Intro from '@components/ui/Intro'
import { Radio, Select, Options } from '@components/form'

import { ReactComponent as IconFuelElectricity } from '@assets/icons/fuelElectricity.svg'
import { ReactComponent as IconFuelGas } from '@assets/icons/fuelGas.svg'
import { ReactComponent as IconFuelOil } from '@assets/icons/fuelOil.svg'
import { ReactComponent as IconFuelPropane } from '@assets/icons/fuelPropane.svg'

const HvacSales = ({ currentStepIndex, setValidationSchema, values }) => {
  const options = {
    maybeBool: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
      { value: 'maybe', label: 'Unsure' },
    ],
    reasonHere: [
      {
        value: 'heat',
        label: 'Heating Equipment (Furnace, Boiler),',
      },
      {
        value: 'dms',
        label: 'Heat Pumps (Ductless Mini Split, Central Heat Pump)',
      },
    ],
    rentOrOwn: [
      {
        value: 'rent',
        label: 'Rent',
      },
      {
        value: 'own',
        label: 'Owner',
      },
    ],
    homeAge: [
      {
        value: 'new',
        label: 'New Construction',
      },
      {
        value: '1-10',
        label: '1-10 Years',
      },
      {
        value: '11-20',
        label: '11-20 Years',
      },
      {
        value: '20+',
        label: '20+ Years',
      },
    ],
    familySize: [
      {
        value: '1',
        label: 'Single-family',
      },
      {
        value: '1-4',
        label: '1-4 Multi-Family',
      },
      {
        value: '5+',
        label: '5+ Family',
      },
    ],
    howHeated: [
      {
        value: 'Gas',
        label: 'Gas',
        description: "You don't need to refuel your heating system.",
        icon: <IconFuelGas />,
      },
      {
        value: 'Oil',
        label: 'Oil',
        description: 'You have a truck that comes and delivers to your home.',
        icon: <IconFuelOil />,
      },
      {
        value: 'Propane',
        label: 'Propane',
        description: 'You have a large tank outside your home.',
        icon: <IconFuelPropane />,
      },
      {
        value: 'Electric',
        label: 'Electricity',
        description:
          "Your equipment connects directly to your home's electric system.",
        icon: <IconFuelElectricity />,
      },
    ],
  }

  useEffect(() => {
    setValidationSchema(
      yup.object().shape({
        reasonHere: yup
          .string()
          .oneOf(options.reasonHere.map(({ value }) => value))
          .when('salesOrService', {
            is: 'sales',
            then: yup.string().required('This field is required.'),
          }),
        existingDuctWork: yup
          .string()
          .oneOf(options.maybeBool.map(({ value }) => value))
          .when('reasonHere', {
            is: 'ac',
            then: yup.string().required('This field is required'),
          }),
        rentOrOwn: yup
          .string()
          .oneOf(options.rentOrOwn.map(({ value }) => value))
          .when(['salesOrService', 'existingDuctWork', 'reasonHere'], {
            is: (salesOrService, existingDuctWork, reasonHere) =>
              salesOrService === 'sales' &&
              (existingDuctWork || reasonHere !== 'ac'),
            then: yup.string().required('This field is required.'),
          }),
        homeAge: yup
          .string()
          .oneOf(options.homeAge.map(({ value }) => value))
          .when(['salesOrService', 'rentOrOwn'], {
            is: (salesOrService, rentOrOwn) =>
              salesOrService === 'sales' && rentOrOwn === 'own',
            then: yup.string().required('This field is required.'),
          }),
        familySize: yup
          .string()
          .oneOf(options.familySize.map(({ value }) => value))
          .when(['homeAge', 'rentOrOwn'], {
            is: (homeAge, rentOrOwn) =>
              ['1-10', '11-20', '20+'].includes(homeAge)
                ? true
                : false && rentOrOwn && rentOrOwn !== 'rent',
            then: yup.string().required('This field is required.'),
          }),
        howHeated: yup
          .string()
          .oneOf(options.howHeated.map(({ value }) => value))
          .when(['familySize'], {
            is: (familySize) => familySize && ['1', '1-4'].includes(familySize),
            then: yup.string().required('This field is required.'),
          }),
      })
    )
  }, [currentStepIndex, values.system])
  const renderFamilySize = () => {
    if (['1-10', '11-20', '20+'].includes(values.homeAge)) return true
    return false
  }

  return (
    <>
      <div>
        <ConditionalBlock render={values.salesOrService === 'sales'}>
          <Field
            component={Radio}
            maxColumns={2}
            label="What kind of equipment are you looking for?"
            name="reasonHere"
            options={options.reasonHere}
          />
        </ConditionalBlock>
        <ConditionalBlock render={values.reasonHere === 'ac'}>
          <Field
            component={Radio}
            maxColumns={2}
            label="Do you have pre-existing ductwork?"
            name="existingDuctWork"
            options={options.maybeBool}
          />
        </ConditionalBlock>
        <ConditionalBlock
          render={
            values.reasonHere &&
            (values.reasonHere !== 'ac' || values.existingDuctWork)
          }
        >
          <Intro title="Home Information" />
          <Field
            component={Radio}
            label="Do you rent or own?"
            maxColumns={2}
            name="rentOrOwn"
            options={options.rentOrOwn}
          />
        </ConditionalBlock>
        <ConditionalBlock render={values.rentOrOwn === 'own'}>
          <Field
            component={Radio}
            label="What is the age of your home?"
            maxColumns={2}
            name="homeAge"
            options={options.homeAge}
          />
        </ConditionalBlock>
      </div>
      <br />
      <ConditionalBlock render={renderFamilySize()}>
        <Field
          component={Select}
          label="Is your home a single family residence, or does it have multiple units?"
          maxColumns={2}
          name="familySize"
          options={options.familySize}
        />
      </ConditionalBlock>
      <ConditionalBlock
        render={values.familySize && ['1', '1-4'].includes(values.familySize)}
      >
        <Field
          centered
          component={Options}
          label="How is your home heated?"
          maxColumns={2}
          name="howHeated"
          variant="square"
          options={options.howHeated}
        />
      </ConditionalBlock>
    </>
  )
}

HvacSales.propTypes = {
  currentStepIndex: PropTypes.number.isRequired,
  setValidationSchema: PropTypes.func.isRequired,
  values: PropTypes.object.isRequired,
}

export default HvacSales
