import config from '@src/config'

const isIneligibleTest = (values) => {
  const ineligibleReason = config.ineligibleValues.find(({ when, is }) => {
    const whenIsArr = Array.isArray(when)
    const isIsFunc = typeof is === 'function'

    if ((!whenIsArr && isIsFunc) || (whenIsArr && !isIsFunc)) {
      console.error(
        "Either both 'when' and 'is' have to be strings or they have to be a string array and a function respectively"
      )
      return true
    }

    if (whenIsArr && isIsFunc) {
      return is(...when.map((entry) => values[entry]))
    }

    if (!whenIsArr && !isIsFunc) {
      return values[when] && values[when] === is
    }
  })

  return ineligibleReason || false
}

export default isIneligibleTest
